#!/usr/bin/env python3
"""
清理后的配置文件
基于OID的Modbus SNMP Agent配置
"""

# =============================================================================
# Modbus通信配置
# =============================================================================

# Modbus通信类型: 'TCP' 或 'RTU'
MODBUS_TYPE = 'TCP'

# Modbus TCP配置
MODBUS_TCP_CONFIG = {
    'server_ip': '127.0.0.1',        # Modbus TCP设备IP地址
    'port': 502,                     # Modbus TCP端口
    'timeout': 3,                    # 连接超时时间(秒)
    'retry_interval': 10,            # 连接失败重试间隔(秒)
    'update_interval': 5,            # 数据更新间隔(秒)
}

# Modbus RTU配置
MODBUS_RTU_CONFIG = {
    'port': 'COM1',                  # 串口端口 (Windows: COM1, Linux: /dev/ttyUSB0)
    'baudrate': 9600,                # 波特率
    'bytesize': 8,                   # 数据位
    'parity': 'N',                   # 校验位 ('N'=无, 'E'=偶, 'O'=奇)
    'stopbits': 1,                   # 停止位
    'timeout': 3,                    # 连接超时时间(秒)
    'retry_interval': 10,            # 连接失败重试间隔(秒)
    'update_interval': 5,            # 数据更新间隔(秒)
}

# =============================================================================
# SNMP OID映射配置
# 每个OID配置对应的Modbus读取方式和数据解析规则
# =============================================================================

SNMP_OID_MAPPING = [
    
    # -------------------------------------------------------------------------
    # 模拟量传感器 - 数值处理类型 (原数据乘以系数)
    # -------------------------------------------------------------------------
    
    {
        'oid': '*******.4.1.99999.1.0',
        'description': '温度传感器1 (°C)',
        'modbus_config': {
            'register_address': 0x100,       # Modbus寄存器地址
            'unit_id': 1,                    # 从站地址
            'function_code': 3,              # 功能码 (3=读保持寄存器)
            'data_type': 'uint16',           # 数据类型
            'register_count': 1              # 读取寄存器数量
        },
        'data_processing': {
            'type': 'multiply',              # 处理类型: 乘以系数
            'coefficient': 0.1,              # 系数: 原数据 * 0.1
            'offset': 0,                     # 偏移量
            'decimal_places': 1              # 小数位数
        },
        'fault_value': -99998               # 通讯故障时的返回值
    },
    
    {
        'oid': '*******.4.1.99999.2.0',
        'description': '湿度传感器1 (%RH)',
        'modbus_config': {
            'register_address': 0x101,
            'unit_id': 1,
            'function_code': 3,
            'data_type': 'uint16',
            'register_count': 1
        },
        'data_processing': {
            'type': 'multiply',
            'coefficient': 0.01,             # 原数据 * 0.01
            'offset': 0,
            'decimal_places': 2
        },
        'fault_value': -99998
    },
    
   
    
    # -------------------------------------------------------------------------
    # 开关量状态 - 等值判断类型 (等于某值时返回1，否则返回0)
    # -------------------------------------------------------------------------
    
    {
        'oid': '*******.4.1.99999.3.0',
        'description': '泵1运行状态',
        'modbus_config': {
            'register_address': 0x102,
            'unit_id': 1,
            'function_code': 3,
            'data_type': 'uint16',
            'register_count': 1
        },
        'data_processing': {
            'type': 'equal_return',          # 处理类型: 等值判断
            'trigger_value': 1,              # 触发值: 当原数据=1时
            'return_value': 1,               # 返回值: 返回1
            'default_value': 0               # 默认值: 否则返回0
        },
        'fault_value': -99998
    },
   
    # -------------------------------------------------------------------------
    # 报警状态 - 同一寄存器的不同值对应不同OID
    # -------------------------------------------------------------------------
    
    {
        'oid': '*******.4.1.99999.4.0',
        'description': '常用电源运行 ',
        'modbus_config': {
            'register_address': 0x103,       # 同一个寄存器
            'unit_id': 2,
            'function_code': 3,
            'data_type': 'uint16',
            'register_count': 1
        },
        'data_processing': {
            'type': 'equal_return',
            'trigger_value': 1,              # 当寄存器值=1，常用电源运行
            'return_value': 1,
            'default_value': 0
        },
        'fault_value': -99998
    },
    
    {
        'oid': '*******.4.1.99999.5.0',
        'description': '备用电源运行 ',
        'modbus_config': {
            'register_address': 0x103,       # 同一个寄存器
            'unit_id': 2,
            'function_code': 3,
            'data_type': 'uint16',
            'register_count': 1
        },
        'data_processing': {
            'type': 'equal_return',
            'trigger_value': 2,              # 当寄存器值=2时，备用电源运行
            'return_value': 1,
            'default_value': 0
        },
        'fault_value': -99998
    },
    
    # -------------------------------------------------------------------------
    # 32位数据处理
    # -------------------------------------------------------------------------
    
    #{
    #    'oid': '*******.4.1.99999.30.0',
    #    'description': '流量计累计流量 (L)',
    #    'modbus_config': {
    #        'register_address': 0x400,
    #        'unit_id': 3,
    #        'function_code': 4,              # 读输入寄存器
    #        'data_type': 'uint32',           # 32位数据，需要读取2个寄存器
    #        'register_count': 2
    #    },
    #    'data_processing': {
    #        'type': 'multiply',
    #        'coefficient': 1,                # 原数据不变
    #        'offset': 0,
    #        'decimal_places': 0
    #    },
    #    'fault_value': -99998
    #},
    
    # -------------------------------------------------------------------------
    # 线圈状态 - 直接映射
    # -------------------------------------------------------------------------
    
    #{
    #    'oid': '*******.4.1.99999.40.0',
    #    'description': '阀门1开关状态',
    #    'modbus_config': {
    #        'register_address': 0x001,
    #        'unit_id': 1,
    #        'function_code': 1,              # 读线圈
    #        'data_type': 'bool',
    #        'register_count': 1
    #    },
    #    'data_processing': {
    #        'type': 'direct'                 # 直接返回原数据 (0或1)
    #    },
    #    'fault_value': -99998
    #},
    
    # -------------------------------------------------------------------------
    # 通讯状态OID
    # -------------------------------------------------------------------------
    
    {
        'oid': '*******.4.1.99999.999.1',
        'description': 'Modbus通讯状态 (全局)',
        'modbus_config': None,               # 通讯状态不需要读取寄存器
        'data_processing': {
            'type': 'communication_status',
            'scope': 'global'                # 全局通讯状态
        },
        'fault_value': 0                     # 通讯故障时为0，正常时为1
    }#,
    
    # {
    #     'oid': '*******.4.1.99999.999.2',
    #     'description': '从站1通讯状态',
    #     'modbus_config': None,
    #     'data_processing': {
    #         'type': 'communication_status',
    #         'scope': 'unit',
    #         'unit_id': 1                     # 特定从站的通讯状态
    #     },
    #     'fault_value': 0
    # },
    
    # {
    #     'oid': '*******.4.1.99999.999.3',
    #     'description': '从站2通讯状态',
    #     'modbus_config': None,
    #     'data_processing': {
    #         'type': 'communication_status',
    #         'scope': 'unit',
    #         'unit_id': 2
    #     },
    #     'fault_value': 0
    # }
]

# =============================================================================
# SNMP服务配置
# =============================================================================

SNMP_CONFIG = {
    'community': 'public',                    # 社区字符串
    'port': 161,                             # SNMP监听端口
    'bind_address': '0.0.0.0',               # 绑定地址
}

# =============================================================================
# 系统配置
# =============================================================================

# 日志配置
LOG_CONFIG = {
    'level': 'INFO',                         # 日志级别: DEBUG, INFO, WARNING, ERROR
    'format': '%(asctime)s - %(levelname)s - %(message)s',
    'file': None,                            # 日志文件路径，None表示输出到控制台
}

# 其他配置
MISC_CONFIG = {
    'startup_delay': 2,                      # 启动延迟(秒)
    'error_value': -99998,                   # 默认错误值
}
