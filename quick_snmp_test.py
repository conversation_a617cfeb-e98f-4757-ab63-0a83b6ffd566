#!/usr/bin/env python3
"""
快速SNMP测试脚本
用于快速验证SNMP Agent是否正常工作
"""

import time
from pysnmp.hlapi import getCmd, SnmpEngine, CommunityData, UdpTransportTarget, ContextData, ObjectType, ObjectIdentity
from config import SNMP_OID_MAPPING, SNMP_CONFIG

def quick_snmp_get(oid, host='127.0.0.1', port=161, community='public'):
    """快速SNMP GET请求"""
    try:
        for (errorIndication, errorStatus, errorIndex, varBinds) in getCmd(
            SnmpEngine(),
            CommunityData(community),
            UdpTransportTarget((host, port)),
            ContextData(),
            ObjectType(ObjectIdentity(oid))
        ):
            if errorIndication:
                return False, f"连接错误: {errorIndication}"
            elif errorStatus:
                return False, f"SNMP错误: {errorStatus.prettyPrint()}"
            else:
                for varBind in varBinds:
                    return True, varBind[1].prettyPrint()
    except Exception as e:
        return False, f"异常: {e}"
    
    return False, "未知错误"

def test_snmp_agent():
    """测试SNMP Agent"""
    print("快速SNMP Agent测试")
    print("=" * 50)
    
    # 从配置读取参数
    host = '127.0.0.1'
    port = SNMP_CONFIG.get('port', 161)
    community = SNMP_CONFIG.get('community', 'public')
    
    print(f"目标: {host}:{port}")
    print(f"社区字符串: {community}")
    print()
    
    # 测试几个代表性的OID
    test_oids = []
    
    # 选择不同类型的OID进行测试
    for config in SNMP_OID_MAPPING:
        proc_type = config.get('data_processing', {}).get('type', 'direct')
        
        # 每种类型选择一个
        if proc_type == 'multiply' and not any(t['type'] == 'multiply' for t in test_oids):
            test_oids.append({
                'oid': config['oid'],
                'description': config['description'],
                'type': 'multiply'
            })
        elif proc_type == 'equal_return' and not any(t['type'] == 'equal_return' for t in test_oids):
            test_oids.append({
                'oid': config['oid'],
                'description': config['description'],
                'type': 'equal_return'
            })
        elif proc_type == 'communication_status' and not any(t['type'] == 'communication_status' for t in test_oids):
            test_oids.append({
                'oid': config['oid'],
                'description': config['description'],
                'type': 'communication_status'
            })
        elif proc_type == 'direct' and not any(t['type'] == 'direct' for t in test_oids):
            test_oids.append({
                'oid': config['oid'],
                'description': config['description'],
                'type': 'direct'
            })
        
        # 最多测试4个不同类型的OID
        if len(test_oids) >= 4:
            break
    
    # 如果没有找到足够的类型，添加前几个OID
    if len(test_oids) < 3:
        for config in SNMP_OID_MAPPING[:3]:
            if not any(t['oid'] == config['oid'] for t in test_oids):
                test_oids.append({
                    'oid': config['oid'],
                    'description': config['description'],
                    'type': config.get('data_processing', {}).get('type', 'direct')
                })
    
    success_count = 0
    total_count = len(test_oids)
    
    for i, test_oid in enumerate(test_oids, 1):
        print(f"测试 {i}/{total_count}: {test_oid['description']}")
        print(f"OID: {test_oid['oid']}")
        print(f"类型: {test_oid['type']}")
        
        success, result = quick_snmp_get(test_oid['oid'], host, port, community)
        
        if success:
            print(f"✓ 结果: {result}")
            success_count += 1
        else:
            print(f"✗ 错误: {result}")
        
        print("-" * 40)
        time.sleep(1)  # 避免请求过快
    
    # 显示总结
    print(f"测试完成: {success_count}/{total_count} 成功")
    
    if success_count == total_count:
        print("🎉 所有测试通过！SNMP Agent工作正常")
    elif success_count > 0:
        print("⚠️  部分测试通过，请检查失败的OID")
    else:
        print("❌ 所有测试失败，请检查SNMP Agent是否启动")
    
    return success_count, total_count

def test_connection():
    """测试SNMP连接"""
    print("测试SNMP连接")
    print("=" * 30)
    
    host = '127.0.0.1'
    port = SNMP_CONFIG.get('port', 161)
    community = SNMP_CONFIG.get('community', 'public')
    
    # 使用第一个配置的OID进行连接测试
    if SNMP_OID_MAPPING:
        test_oid = SNMP_OID_MAPPING[0]['oid']
        print(f"使用OID {test_oid} 测试连接...")
        
        success, result = quick_snmp_get(test_oid, host, port, community)
        
        if success:
            print("✓ 连接成功")
            return True
        else:
            print(f"✗ 连接失败: {result}")
            return False
    else:
        print("❌ 没有配置的OID可用于测试")
        return False

def show_quick_help():
    """显示快速帮助"""
    print("快速SNMP测试工具")
    print("=" * 30)
    print("用法:")
    print("  python quick_snmp_test.py              # 运行快速测试")
    print("  python quick_snmp_test.py connection   # 仅测试连接")
    print("  python quick_snmp_test.py help         # 显示帮助")
    print()
    print("注意: 请确保SNMP Agent已启动")

def main():
    """主函数"""
    import sys
    
    if len(sys.argv) > 1:
        command = sys.argv[1].lower()
        
        if command == 'connection':
            test_connection()
        elif command == 'help':
            show_quick_help()
        else:
            print(f"未知命令: {command}")
            show_quick_help()
    else:
        # 默认运行快速测试
        try:
            test_snmp_agent()
        except KeyboardInterrupt:
            print("\n测试被用户中断")
        except Exception as e:
            print(f"测试过程中发生错误: {e}")

if __name__ == "__main__":
    main()
