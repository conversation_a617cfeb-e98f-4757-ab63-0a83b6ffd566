#!/usr/bin/env python3
"""
SNMP GET测试脚本
用于测试SNMP Agent的GET请求功能
"""

import sys
import time
import asyncio
from pysnmp.hlapi.asyncio import *
from config import SNMP_OID_MAPPING, SNMP_CONFIG

class SNMPGetTester:
    """SNMP GET测试器"""
    
    def __init__(self, host='127.0.0.1', port=161, community='public'):
        self.host = host
        self.port = port
        self.community = community
        self.test_results = []
    
    async def get_oid(self, oid, description=""):
        """异步获取单个OID的值"""
        try:
            async for (errorIndication, errorStatus, errorIndex, varBinds) in getCmd(
                SnmpEngine(),
                CommunityData(self.community),
                await UdpTransportTarget.create((self.host, self.port)),
                ContextData(),
                ObjectType(ObjectIdentity(oid))
            ):
                if errorIndication:
                    return {
                        'oid': oid,
                        'description': description,
                        'success': False,
                        'error': f"错误指示: {errorIndication}",
                        'value': None
                    }
                elif errorStatus:
                    return {
                        'oid': oid,
                        'description': description,
                        'success': False,
                        'error': f"状态错误: {errorStatus.prettyPrint()} at {errorIndex and varBinds[int(errorIndex) - 1][0] or '?'}",
                        'value': None
                    }
                else:
                    for varBind in varBinds:
                        value = varBind[1].prettyPrint()
                        return {
                            'oid': oid,
                            'description': description,
                            'success': True,
                            'error': None,
                            'value': value
                        }
        except Exception as e:
            return {
                'oid': oid,
                'description': description,
                'success': False,
                'error': f"异常: {e}",
                'value': None
            }
    
    async def test_single_oid(self, oid, description=""):
        """测试单个OID"""
        print(f"测试OID: {oid}")
        if description:
            print(f"描述: {description}")
        
        result = await self.get_oid(oid, description)
        
        if result['success']:
            print(f"✓ 成功: {result['value']}")
        else:
            print(f"✗ 失败: {result['error']}")
        
        print("-" * 50)
        self.test_results.append(result)
        return result
    
    async def test_all_configured_oids(self):
        """测试所有配置的OID"""
        print("测试所有配置的OID")
        print("=" * 60)
        
        for config in SNMP_OID_MAPPING:
            oid = config['oid']
            description = config['description']
            
            await self.test_single_oid(oid, description)
            await asyncio.sleep(0.5)  # 避免请求过快
    
    async def test_specific_oids(self, oid_list):
        """测试指定的OID列表"""
        print("测试指定的OID")
        print("=" * 60)
        
        for oid in oid_list:
            # 查找描述
            description = ""
            for config in SNMP_OID_MAPPING:
                if config['oid'] == oid:
                    description = config['description']
                    break
            
            await self.test_single_oid(oid, description)
            await asyncio.sleep(0.5)
    
    async def test_by_category(self, category):
        """按类别测试OID"""
        print(f"测试{category}类型的OID")
        print("=" * 60)
        
        category_map = {
            'multiply': '数值处理',
            'equal_return': '状态判断',
            'direct': '直接映射',
            'communication_status': '通讯状态'
        }
        
        found_oids = []
        for config in SNMP_OID_MAPPING:
            processing_type = config.get('data_processing', {}).get('type', 'direct')
            if processing_type == category:
                found_oids.append(config)
        
        if not found_oids:
            print(f"未找到{category_map.get(category, category)}类型的OID")
            return
        
        print(f"找到 {len(found_oids)} 个{category_map.get(category, category)}类型的OID:")
        print()
        
        for config in found_oids:
            await self.test_single_oid(config['oid'], config['description'])
            await asyncio.sleep(0.5)
    
    async def continuous_monitoring(self, oid_list, interval=5, duration=60):
        """连续监控指定OID的值变化"""
        print(f"连续监控OID值变化 (间隔: {interval}秒, 持续: {duration}秒)")
        print("=" * 60)
        
        start_time = time.time()
        round_count = 0
        
        while time.time() - start_time < duration:
            round_count += 1
            print(f"\n第 {round_count} 轮监控 ({time.strftime('%H:%M:%S')}):")
            print("-" * 40)
            
            for oid in oid_list:
                # 查找描述
                description = ""
                for config in SNMP_OID_MAPPING:
                    if config['oid'] == oid:
                        description = config['description']
                        break
                
                result = await self.get_oid(oid, description)
                if result['success']:
                    print(f"{description}: {result['value']}")
                else:
                    print(f"{description}: 错误 - {result['error']}")
            
            await asyncio.sleep(interval)
        
        print(f"\n监控完成，共进行了 {round_count} 轮")
    
    def show_test_summary(self):
        """显示测试总结"""
        if not self.test_results:
            print("没有测试结果")
            return
        
        print("\n测试总结")
        print("=" * 60)
        
        total_tests = len(self.test_results)
        successful_tests = sum(1 for result in self.test_results if result['success'])
        failed_tests = total_tests - successful_tests
        
        print(f"总测试数: {total_tests}")
        print(f"成功: {successful_tests}")
        print(f"失败: {failed_tests}")
        print(f"成功率: {successful_tests/total_tests*100:.1f}%")
        
        if failed_tests > 0:
            print("\n失败的测试:")
            for result in self.test_results:
                if not result['success']:
                    print(f"  {result['oid']} ({result['description']}): {result['error']}")
    
    def show_available_oids(self):
        """显示所有可用的OID"""
        print("可用的OID列表")
        print("=" * 60)
        
        # 按类型分组
        categories = {}
        for config in SNMP_OID_MAPPING:
            processing_type = config.get('data_processing', {}).get('type', 'direct')
            if processing_type not in categories:
                categories[processing_type] = []
            categories[processing_type].append(config)
        
        category_names = {
            'multiply': '数值处理 (乘以系数)',
            'equal_return': '状态判断 (等值返回)',
            'direct': '直接映射',
            'communication_status': '通讯状态'
        }
        
        for category, configs in categories.items():
            print(f"\n{category_names.get(category, category)}:")
            print("-" * 40)
            for config in configs:
                print(f"  {config['oid']} - {config['description']}")

async def main():
    """主函数"""
    # 从配置文件读取SNMP参数
    host = '127.0.0.1'
    port = SNMP_CONFIG.get('port', 161)
    community = SNMP_CONFIG.get('community', 'public')
    
    tester = SNMPGetTester(host, port, community)
    
    if len(sys.argv) < 2:
        print("SNMP GET测试工具")
        print("=" * 60)
        print("用法:")
        print("  python test_snmp_get.py all                    # 测试所有OID")
        print("  python test_snmp_get.py list                   # 显示所有可用OID")
        print("  python test_snmp_get.py oid <OID>              # 测试指定OID")
        print("  python test_snmp_get.py category <类型>        # 测试指定类型")
        print("  python test_snmp_get.py monitor <OID1,OID2>    # 监控指定OID")
        print()
        print("类型选项: multiply, equal_return, direct, communication_status")
        print()
        print("示例:")
        print("  python test_snmp_get.py oid *******.4.1.99999.1.0")
        print("  python test_snmp_get.py category multiply")
        print("  python test_snmp_get.py monitor *******.4.1.99999.1.0,*******.4.1.99999.999.1")
        return
    
    command = sys.argv[1].lower()
    
    try:
        if command == 'all':
            await tester.test_all_configured_oids()
            tester.show_test_summary()
        
        elif command == 'list':
            tester.show_available_oids()
        
        elif command == 'oid':
            if len(sys.argv) < 3:
                print("请指定要测试的OID")
                return
            oid = sys.argv[2]
            await tester.test_single_oid(oid)
        
        elif command == 'category':
            if len(sys.argv) < 3:
                print("请指定要测试的类型")
                print("可用类型: multiply, equal_return, direct, communication_status")
                return
            category = sys.argv[2]
            await tester.test_by_category(category)
            tester.show_test_summary()
        
        elif command == 'monitor':
            if len(sys.argv) < 3:
                print("请指定要监控的OID列表 (用逗号分隔)")
                return
            oid_list = [oid.strip() for oid in sys.argv[2].split(',')]
            interval = 5
            duration = 60
            
            # 可选参数
            if len(sys.argv) >= 4:
                interval = int(sys.argv[3])
            if len(sys.argv) >= 5:
                duration = int(sys.argv[4])
            
            await tester.continuous_monitoring(oid_list, interval, duration)
        
        else:
            print(f"未知命令: {command}")
            print("使用 'python test_snmp_get.py' 查看帮助")
    
    except KeyboardInterrupt:
        print("\n测试被用户中断")
    except Exception as e:
        print(f"测试过程中发生错误: {e}")

if __name__ == "__main__":
    asyncio.run(main())
