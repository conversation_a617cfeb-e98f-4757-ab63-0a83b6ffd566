#!/usr/bin/env python3
"""
配置测试脚本
测试清理后的配置文件结构
"""

import sys
from config import SNMP_OID_MAPPING, SNMP_CONFIG, MODBUS_TYPE

def show_config_summary():
    """显示配置概要"""
    print("清理后的配置文件测试")
    print("=" * 60)
    print("配置特点:")
    print("✓ 以SNMP OID为主导的配置结构")
    print("✓ 删除了不必要的oid_mapping字段")
    print("✓ 统一的OID映射配置")
    print("✓ 支持多种数据处理类型")
    print("✓ 设备通讯中断时返回-99998")
    print()
    
    # 统计配置
    total_oids = len(SNMP_OID_MAPPING)
    type_counts = {}
    
    for config in SNMP_OID_MAPPING:
        proc_type = config.get('data_processing', {}).get('type', 'unknown')
        type_counts[proc_type] = type_counts.get(proc_type, 0) + 1
    
    print(f"总计配置: {total_oids} 个OID")
    print("按处理类型分布:")
    for proc_type, count in type_counts.items():
        print(f"  - {proc_type}: {count} 个")
    print()

def show_multiply_examples():
    """显示数值处理示例"""
    print("数值处理配置 (multiply):")
    print("-" * 40)
    
    for config in SNMP_OID_MAPPING:
        if config.get('data_processing', {}).get('type') == 'multiply':
            processing = config['data_processing']
            modbus_cfg = config['modbus_config']
            
            print(f"OID: {config['oid']}")
            print(f"描述: {config['description']}")
            print(f"寄存器: 0x{modbus_cfg['register_address']:04X} (从站{modbus_cfg['unit_id']})")
            
            coefficient = processing['coefficient']
            offset = processing.get('offset', 0)
            decimal_places = processing.get('decimal_places', 0)
            
            print(f"处理公式: (原值 × {coefficient}) + {offset}")
            print(f"小数位数: {decimal_places}")
            print(f"故障值: {config['fault_value']}")
            
            # 示例计算
            test_value = 1000
            result = (test_value * coefficient) + offset
            if decimal_places > 0:
                result = round(result, decimal_places)
            else:
                result = int(result)
            print(f"示例: 原值{test_value} → 处理后{result}")
            print()

def show_equal_return_examples():
    """显示状态判断示例"""
    print("状态判断配置 (equal_return):")
    print("-" * 40)
    
    for config in SNMP_OID_MAPPING:
        if config.get('data_processing', {}).get('type') == 'equal_return':
            processing = config['data_processing']
            modbus_cfg = config['modbus_config']
            
            print(f"OID: {config['oid']}")
            print(f"描述: {config['description']}")
            print(f"寄存器: 0x{modbus_cfg['register_address']:04X} (从站{modbus_cfg['unit_id']})")
            
            trigger_value = processing['trigger_value']
            return_value = processing['return_value']
            default_value = processing['default_value']
            
            print(f"判断逻辑: 当原值={trigger_value}时返回{return_value}, 否则返回{default_value}")
            print(f"故障值: {config['fault_value']}")
            print()

def show_communication_status():
    """显示通讯状态配置"""
    print("通讯状态配置 (communication_status):")
    print("-" * 40)
    
    for config in SNMP_OID_MAPPING:
        if config.get('data_processing', {}).get('type') == 'communication_status':
            processing = config['data_processing']
            
            print(f"OID: {config['oid']}")
            print(f"描述: {config['description']}")
            print(f"监控范围: {processing['scope']}")
            
            if processing.get('unit_id'):
                print(f"从站ID: {processing['unit_id']}")
            
            print(f"正常值: 1, 故障值: 0")
            print()

def show_same_register_mapping():
    """显示同一寄存器的多OID映射"""
    print("同一寄存器的多OID映射:")
    print("-" * 40)
    
    # 按寄存器地址分组
    register_groups = {}
    
    for config in SNMP_OID_MAPPING:
        modbus_cfg = config.get('modbus_config')
        if modbus_cfg:
            key = (modbus_cfg['register_address'], modbus_cfg['unit_id'])
            if key not in register_groups:
                register_groups[key] = []
            register_groups[key].append(config)
    
    # 显示有多个OID的寄存器
    for (reg_addr, unit_id), configs in register_groups.items():
        if len(configs) > 1:
            print(f"寄存器 0x{reg_addr:04X} (从站{unit_id}) 对应 {len(configs)} 个OID:")
            for config in configs:
                processing = config.get('data_processing', {})
                if processing.get('type') == 'equal_return':
                    trigger = processing.get('trigger_value', 'N/A')
                    print(f"  {config['oid']} - {config['description']} (触发值: {trigger})")
                else:
                    print(f"  {config['oid']} - {config['description']}")
            print()

def show_test_commands():
    """显示测试命令"""
    print("测试命令:")
    print("-" * 40)
    
    community = SNMP_CONFIG['community']
    port = SNMP_CONFIG['port']
    
    print("1. 启动SNMP Agent:")
    print("   python modbus_snmp_agent.py")
    print()
    
    print("2. 测试SNMP查询:")
    # 显示几个代表性的OID
    count = 0
    for config in SNMP_OID_MAPPING:
        if count >= 3:
            break
        oid = config['oid']
        description = config['description']
        print(f"   # {description}")
        print(f"   snmpget -v2c -c {community} 127.0.0.1:{port} {oid}")
        count += 1
    
    print()
    print("3. 获取所有OID:")
    print(f"   snmpwalk -v2c -c {community} 127.0.0.1:{port} *******.4.1.99999")

def main():
    """主函数"""
    if len(sys.argv) > 1:
        command = sys.argv[1].lower()
        
        if command == 'summary':
            show_config_summary()
        elif command == 'multiply':
            show_multiply_examples()
        elif command == 'equal':
            show_equal_return_examples()
        elif command == 'comm':
            show_communication_status()
        elif command == 'mapping':
            show_same_register_mapping()
        elif command == 'commands':
            show_test_commands()
        else:
            print(f"未知命令: {command}")
            print("可用命令: summary, multiply, equal, comm, mapping, commands")
    else:
        # 默认显示概要
        show_config_summary()
        show_same_register_mapping()
        show_test_commands()
        
        print("\n" + "=" * 60)
        print("详细信息命令:")
        print("python test_config.py summary    # 显示配置概要")
        print("python test_config.py multiply   # 显示数值处理配置")
        print("python test_config.py equal     # 显示状态判断配置")
        print("python test_config.py comm      # 显示通讯状态配置")
        print("python test_config.py mapping   # 显示同寄存器多OID映射")
        print("python test_config.py commands  # 显示测试命令")
        print("=" * 60)

if __name__ == "__main__":
    main()
