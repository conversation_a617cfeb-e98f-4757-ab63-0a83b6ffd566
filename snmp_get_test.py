#!/usr/bin/env python3
"""
SNMP GET测试脚本 - 兼容新版本pysnmp
"""

import sys
import time
import socket
from config import SNMP_OID_MAPPING, SNMP_CONFIG

try:
    # 尝试新版本的pysnmp导入
    from pysnmp.hlapi.v3arch.asyncio import *
    ASYNC_MODE = True
except ImportError:
    try:
        # 尝试旧版本的pysnmp导入
        from pysnmp.hlapi import *
        ASYNC_MODE = False
    except ImportError:
        print("错误: 无法导入pysnmp库")
        print("请安装: pip install pysnmp")
        sys.exit(1)

def test_snmp_connection(host='127.0.0.1', port=161):
    """测试SNMP连接"""
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        sock.settimeout(3)
        result = sock.connect_ex((host, port))
        sock.close()
        return result == 0
    except:
        return False

def simple_snmp_get(oid, host='127.0.0.1', port=161, community='public'):
    """简单的SNMP GET请求"""
    try:
        # 使用同步方式
        for (errorIndication, errorStatus, errorIndex, varBinds) in getCmd(
            SnmpEngine(),
            CommunityData(community),
            UdpTransportTarget((host, port)),
            ContextData(),
            ObjectType(ObjectIdentity(oid))
        ):
            if errorIndication:
                return False, f"连接错误: {errorIndication}"
            elif errorStatus:
                return False, f"SNMP错误: {errorStatus.prettyPrint()}"
            else:
                for varBind in varBinds:
                    return True, varBind[1].prettyPrint()
    except Exception as e:
        return False, f"异常: {e}"
    
    return False, "未知错误"

def test_oid_list():
    """测试OID列表"""
    print("SNMP GET测试")
    print("=" * 50)
    
    # 从配置读取参数
    host = '127.0.0.1'
    port = SNMP_CONFIG.get('port', 161)
    community = SNMP_CONFIG.get('community', 'public')
    
    print(f"目标: {host}:{port}")
    print(f"社区字符串: {community}")
    print()
    
    # 首先测试连接
    print("测试网络连接...")
    if test_snmp_connection(host, port):
        print("✓ 网络连接正常")
    else:
        print("✗ 网络连接失败")
        print("请确保SNMP Agent已启动")
        return
    
    print()
    
    # 选择几个代表性的OID进行测试
    test_oids = [
        ('*******.4.1.99999.1.0', '温度传感器1 (°C)'),
        ('*******.4.1.99999.10.0', '泵1运行状态'),
        ('*******.4.1.99999.999.1', 'Modbus通讯状态 (全局)'),
    ]
    
    # 如果配置中有其他OID，也添加进来
    for config in SNMP_OID_MAPPING[:2]:  # 只取前2个
        oid = config['oid']
        desc = config['description']
        if (oid, desc) not in test_oids:
            test_oids.append((oid, desc))
    
    success_count = 0
    total_count = len(test_oids)
    
    for i, (oid, description) in enumerate(test_oids, 1):
        print(f"测试 {i}/{total_count}: {description}")
        print(f"OID: {oid}")
        
        success, result = simple_snmp_get(oid, host, port, community)
        
        if success:
            print(f"✓ 值: {result}")
            success_count += 1
        else:
            print(f"✗ 错误: {result}")
        
        print("-" * 40)
        time.sleep(1)
    
    # 显示总结
    print(f"测试完成: {success_count}/{total_count} 成功")
    
    if success_count == total_count:
        print("🎉 所有测试通过！SNMP Agent工作正常")
    elif success_count > 0:
        print("⚠️  部分测试通过，请检查失败的OID")
    else:
        print("❌ 所有测试失败，请检查SNMP Agent是否启动")

def test_single_oid(oid):
    """测试单个OID"""
    host = '127.0.0.1'
    port = SNMP_CONFIG.get('port', 161)
    community = SNMP_CONFIG.get('community', 'public')
    
    # 查找描述
    description = "未知"
    for config in SNMP_OID_MAPPING:
        if config['oid'] == oid:
            description = config['description']
            break
    
    print(f"测试单个OID")
    print("=" * 30)
    print(f"OID: {oid}")
    print(f"描述: {description}")
    print(f"目标: {host}:{port}")
    print()
    
    success, result = simple_snmp_get(oid, host, port, community)
    
    if success:
        print(f"✓ 成功: {result}")
    else:
        print(f"✗ 失败: {result}")

def show_available_oids():
    """显示所有可用的OID"""
    print("可用的OID列表")
    print("=" * 50)
    
    # 按类型分组
    types = {}
    for config in SNMP_OID_MAPPING:
        proc_type = config.get('data_processing', {}).get('type', 'direct')
        if proc_type not in types:
            types[proc_type] = []
        types[proc_type].append(config)
    
    type_names = {
        'multiply': '数值处理 (乘以系数)',
        'equal_return': '状态判断 (等值返回)',
        'direct': '直接映射',
        'communication_status': '通讯状态'
    }
    
    for proc_type, configs in types.items():
        print(f"\n{type_names.get(proc_type, proc_type)}:")
        print("-" * 40)
        for config in configs:
            print(f"  {config['oid']} - {config['description']}")

def monitor_oids(oid_list, interval=5, count=5):
    """监控指定OID的值变化"""
    host = '127.0.0.1'
    port = SNMP_CONFIG.get('port', 161)
    community = SNMP_CONFIG.get('community', 'public')
    
    print(f"监控OID值变化")
    print(f"间隔: {interval}秒, 次数: {count}")
    print("=" * 50)
    
    # 获取OID描述
    oid_descriptions = {}
    for oid in oid_list:
        for config in SNMP_OID_MAPPING:
            if config['oid'] == oid:
                oid_descriptions[oid] = config['description']
                break
        if oid not in oid_descriptions:
            oid_descriptions[oid] = "未知"
    
    for i in range(count):
        print(f"\n第 {i+1} 次监控 ({time.strftime('%H:%M:%S')}):")
        print("-" * 30)
        
        for oid in oid_list:
            success, value = simple_snmp_get(oid, host, port, community)
            description = oid_descriptions[oid]
            
            if success:
                print(f"{description}: {value}")
            else:
                print(f"{description}: 错误 - {value}")
        
        if i < count - 1:  # 最后一次不需要等待
            time.sleep(interval)
    
    print(f"\n监控完成")

def main():
    """主函数"""
    if len(sys.argv) < 2:
        print("SNMP GET测试工具")
        print("=" * 30)
        print("用法:")
        print("  python snmp_get_test.py test           # 运行快速测试")
        print("  python snmp_get_test.py list           # 显示所有可用OID")
        print("  python snmp_get_test.py oid <OID>      # 测试指定OID")
        print("  python snmp_get_test.py monitor <OID>  # 监控指定OID")
        print()
        print("示例:")
        print("  python snmp_get_test.py oid *******.4.1.99999.1.0")
        print("  python snmp_get_test.py monitor *******.4.1.99999.999.1")
        return
    
    command = sys.argv[1].lower()
    
    try:
        if command == 'test':
            test_oid_list()
        
        elif command == 'list':
            show_available_oids()
        
        elif command == 'oid':
            if len(sys.argv) < 3:
                print("请指定要测试的OID")
                return
            oid = sys.argv[2]
            test_single_oid(oid)
        
        elif command == 'monitor':
            if len(sys.argv) < 3:
                print("请指定要监控的OID")
                return
            oid = sys.argv[2]
            
            # 可选参数
            interval = 5
            count = 5
            if len(sys.argv) >= 4:
                interval = int(sys.argv[3])
            if len(sys.argv) >= 5:
                count = int(sys.argv[4])
            
            monitor_oids([oid], interval, count)
        
        else:
            print(f"未知命令: {command}")
            print("使用 'python snmp_get_test.py' 查看帮助")
    
    except KeyboardInterrupt:
        print("\n测试被用户中断")
    except Exception as e:
        print(f"测试过程中发生错误: {e}")

if __name__ == "__main__":
    main()
