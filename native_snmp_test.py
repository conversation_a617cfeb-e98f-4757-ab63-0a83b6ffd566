#!/usr/bin/env python3
"""
原生Python SNMP测试
使用原生socket和SNMP协议实现GET请求
"""

import socket
import struct
import time
import sys
from config import SNMP_OID_MAPPING, SNMP_CONFIG

class SimpleSNMPClient:
    """简单的SNMP客户端实现"""
    
    def __init__(self, host='127.0.0.1', port=161, community='public'):
        self.host = host
        self.port = port
        self.community = community.encode('utf-8')
        self.request_id = 1
    
    def encode_oid(self, oid_str):
        """编码OID字符串为字节"""
        parts = [int(x) for x in oid_str.split('.')]
        
        # 第一个字节是前两个数字的组合
        if len(parts) >= 2:
            first_byte = parts[0] * 40 + parts[1]
            encoded = [first_byte]
            parts = parts[2:]
        else:
            encoded = []
        
        # 编码剩余的数字
        for part in parts:
            if part < 128:
                encoded.append(part)
            else:
                # 大于127的数字需要特殊编码
                temp = []
                while part > 0:
                    temp.insert(0, (part & 0x7F))
                    part >>= 7
                
                for i, byte in enumerate(temp):
                    if i < len(temp) - 1:
                        byte |= 0x80
                    encoded.append(byte)
        
        return bytes(encoded)
    
    def encode_length(self, length):
        """编码长度字段"""
        if length < 128:
            return bytes([length])
        else:
            # 长格式编码
            length_bytes = []
            temp = length
            while temp > 0:
                length_bytes.insert(0, temp & 0xFF)
                temp >>= 8
            return bytes([0x80 | len(length_bytes)]) + bytes(length_bytes)
    
    def create_snmp_get_request(self, oid_str):
        """创建SNMP GET请求包"""
        # 编码OID
        oid_encoded = self.encode_oid(oid_str)
        
        # 构建VarBind (OID + NULL值)
        oid_tlv = b'\x06' + self.encode_length(len(oid_encoded)) + oid_encoded
        null_tlv = b'\x05\x00'  # NULL类型
        varbind = b'\x30' + self.encode_length(len(oid_tlv) + len(null_tlv)) + oid_tlv + null_tlv
        
        # 构建VarBindList
        varbind_list = b'\x30' + self.encode_length(len(varbind)) + varbind
        
        # 构建PDU
        request_id_tlv = b'\x02\x01' + bytes([self.request_id & 0xFF])
        error_status_tlv = b'\x02\x01\x00'  # 无错误
        error_index_tlv = b'\x02\x01\x00'   # 无错误索引
        
        pdu_content = request_id_tlv + error_status_tlv + error_index_tlv + varbind_list
        pdu = b'\xa0' + self.encode_length(len(pdu_content)) + pdu_content  # GET请求
        
        # 构建SNMP消息
        version_tlv = b'\x02\x01\x00'  # SNMP v1
        community_tlv = b'\x04' + self.encode_length(len(self.community)) + self.community
        
        message_content = version_tlv + community_tlv + pdu
        message = b'\x30' + self.encode_length(len(message_content)) + message_content
        
        self.request_id += 1
        return message
    
    def parse_snmp_response(self, data):
        """解析SNMP响应包"""
        try:
            # 这是一个简化的解析器，只提取基本信息
            if len(data) < 10:
                return False, "响应包太短"
            
            # 检查是否是SNMP响应
            if data[0] != 0x30:
                return False, "不是有效的SNMP响应"
            
            # 简单提取：查找响应中的值
            # 在实际的SNMP响应中，值通常在包的后半部分
            
            # 查找INTEGER类型的值 (0x02)
            for i in range(len(data) - 5):
                if data[i] == 0x02 and data[i+1] <= 4:  # INTEGER类型，长度<=4
                    length = data[i+1]
                    if i + 2 + length <= len(data):
                        value = 0
                        for j in range(length):
                            value = (value << 8) + data[i + 2 + j]
                        return True, str(value)
            
            # 查找OCTET STRING类型的值 (0x04)
            for i in range(len(data) - 3):
                if data[i] == 0x04 and data[i+1] > 0:
                    length = data[i+1]
                    if i + 2 + length <= len(data):
                        value = data[i+2:i+2+length].decode('utf-8', errors='ignore')
                        return True, value
            
            return True, "收到响应但无法解析值"
            
        except Exception as e:
            return False, f"解析错误: {e}"
    
    def get_oid(self, oid_str):
        """发送SNMP GET请求"""
        try:
            # 创建请求包
            request = self.create_snmp_get_request(oid_str)
            
            # 发送请求
            sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            sock.settimeout(5)
            
            sock.sendto(request, (self.host, self.port))
            
            # 接收响应
            response, addr = sock.recvfrom(1024)
            sock.close()
            
            # 解析响应
            success, result = self.parse_snmp_response(response)
            
            if success:
                return True, result
            else:
                return False, result
                
        except socket.timeout:
            return False, "请求超时"
        except Exception as e:
            return False, f"请求失败: {e}"

def test_snmp_with_native_client():
    """使用原生客户端测试SNMP"""
    print("原生Python SNMP测试")
    print("=" * 50)
    
    host = '127.0.0.1'
    port = SNMP_CONFIG.get('port', 161)
    community = SNMP_CONFIG.get('community', 'public')
    
    print(f"目标: {host}:{port}")
    print(f"社区字符串: {community}")
    print()
    
    client = SimpleSNMPClient(host, port, community)
    
    # 测试几个OID
    test_oids = [
        ('*******.4.1.99999.1.0', '温度传感器1'),
        ('*******.4.1.99999.999.1', '通讯状态'),
        ('*******.4.1.99999.10.0', '泵1运行状态'),
    ]
    
    success_count = 0
    total_count = len(test_oids)
    
    for i, (oid, description) in enumerate(test_oids, 1):
        print(f"测试 {i}/{total_count}: {description}")
        print(f"OID: {oid}")
        
        success, result = client.get_oid(oid)
        
        if success:
            print(f"✓ 值: {result}")
            success_count += 1
        else:
            print(f"✗ 错误: {result}")
        
        print("-" * 40)
        time.sleep(1)
    
    print(f"测试完成: {success_count}/{total_count} 成功")
    
    if success_count > 0:
        print("🎉 SNMP Agent响应正常！")
    else:
        print("❌ 所有测试失败")

def test_raw_udp_communication():
    """测试原始UDP通信"""
    print("原始UDP通信测试")
    print("=" * 30)
    
    host = '127.0.0.1'
    port = SNMP_CONFIG.get('port', 161)
    
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        sock.settimeout(3)
        
        # 发送简单的测试数据
        test_data = b"SNMP_TEST_PACKET"
        sock.sendto(test_data, (host, port))
        
        try:
            response, addr = sock.recvfrom(1024)
            print(f"✓ 收到响应: {len(response)} 字节")
            print(f"  来源: {addr}")
            print(f"  内容: {response[:50]}...")
        except socket.timeout:
            print("⚠️  UDP端口开放但无响应 (正常)")
        
        sock.close()
        return True
        
    except Exception as e:
        print(f"✗ UDP通信失败: {e}")
        return False

def show_snmp_packet_info():
    """显示SNMP包信息"""
    print("SNMP协议信息")
    print("=" * 30)
    
    client = SimpleSNMPClient()
    
    # 创建一个示例请求包
    test_oid = "*******.4.1.99999.1.0"
    packet = client.create_snmp_get_request(test_oid)
    
    print(f"SNMP GET请求包 (OID: {test_oid}):")
    print(f"包大小: {len(packet)} 字节")
    print(f"十六进制: {packet.hex()}")
    print()
    
    print("SNMP包结构:")
    print("- 版本: SNMPv1")
    print("- 社区字符串: public")
    print("- PDU类型: GET请求")
    print("- 请求ID: 自动递增")

def main():
    """主函数"""
    if len(sys.argv) > 1:
        command = sys.argv[1].lower()
        
        if command == 'native':
            test_snmp_with_native_client()
        elif command == 'udp':
            test_raw_udp_communication()
        elif command == 'packet':
            show_snmp_packet_info()
        else:
            print(f"未知命令: {command}")
            print("可用命令: native, udp, packet")
    else:
        # 运行所有测试
        print("原生SNMP测试套件")
        print("=" * 60)
        
        # 1. UDP通信测试
        udp_ok = test_raw_udp_communication()
        print()
        
        # 2. 如果UDP可达，测试SNMP
        if udp_ok:
            test_snmp_with_native_client()
        else:
            print("跳过SNMP测试 (UDP不可达)")
        
        print()
        
        # 3. 显示包信息
        show_snmp_packet_info()
        
        print("\n" + "=" * 60)
        print("测试完成")

if __name__ == "__main__":
    main()
