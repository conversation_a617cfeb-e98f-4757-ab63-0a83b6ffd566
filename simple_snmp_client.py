#!/usr/bin/env python3
"""
简单的SNMP客户端测试
使用snmpget命令行工具进行测试
"""

import sys
import subprocess
import time
from config import SNMP_OID_MAPPING, SNMP_CONFIG

def run_snmpget(oid, host='127.0.0.1', port=161, community='public'):
    """使用snmpget命令行工具"""
    try:
        cmd = [
            'snmpget',
            '-v2c',
            '-c', community,
            f'{host}:{port}',
            oid
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0:
            # 解析输出
            output = result.stdout.strip()
            if '=' in output:
                parts = output.split('=', 1)
                if len(parts) == 2:
                    value_part = parts[1].strip()
                    # 移除类型信息，只保留值
                    if ':' in value_part:
                        value = value_part.split(':', 1)[1].strip()
                    else:
                        value = value_part
                    return True, value
            return True, output
        else:
            error_msg = result.stderr.strip() if result.stderr else "未知错误"
            return False, error_msg
    
    except subprocess.TimeoutExpired:
        return False, "请求超时"
    except FileNotFoundError:
        return False, "snmpget命令未找到，请安装net-snmp工具"
    except Exception as e:
        return False, f"异常: {e}"

def test_with_python_snmp():
    """尝试使用Python SNMP库"""
    try:
        # 尝试不同的导入方式
        try:
            from pysnmp.hlapi.v1arch import cmdgen
            print("使用pysnmp v1arch")
            return test_with_cmdgen(cmdgen)
        except ImportError:
            pass
        
        try:
            from pysnmp.entity.rfc3413.oneliner import cmdgen
            print("使用pysnmp entity")
            return test_with_cmdgen(cmdgen)
        except ImportError:
            pass
        
        try:
            from pysnmp import cmdgen
            print("使用pysnmp cmdgen")
            return test_with_cmdgen(cmdgen)
        except ImportError:
            pass
        
        return False, "无法导入pysnmp"
    
    except Exception as e:
        return False, f"Python SNMP测试失败: {e}"

def test_with_cmdgen(cmdgen):
    """使用cmdgen进行测试"""
    try:
        host = '127.0.0.1'
        port = SNMP_CONFIG.get('port', 161)
        community = SNMP_CONFIG.get('community', 'public')
        
        # 测试第一个OID
        if SNMP_OID_MAPPING:
            test_oid = SNMP_OID_MAPPING[0]['oid']
            
            cmdGen = cmdgen.CommandGenerator()
            errorIndication, errorStatus, errorIndex, varBinds = cmdGen.getCmd(
                cmdgen.CommunityData(community),
                cmdgen.UdpTransportTarget((host, port)),
                test_oid
            )
            
            if errorIndication:
                return False, f"错误指示: {errorIndication}"
            elif errorStatus:
                return False, f"错误状态: {errorStatus}"
            else:
                for name, val in varBinds:
                    return True, f"{name} = {val}"
        
        return False, "没有可测试的OID"
    
    except Exception as e:
        return False, f"cmdgen测试失败: {e}"

def test_snmp_agent():
    """测试SNMP Agent"""
    print("SNMP Agent测试")
    print("=" * 50)
    
    host = '127.0.0.1'
    port = SNMP_CONFIG.get('port', 161)
    community = SNMP_CONFIG.get('community', 'public')
    
    print(f"目标: {host}:{port}")
    print(f"社区字符串: {community}")
    print()
    
    # 选择几个测试OID
    test_oids = []
    
    # 每种类型选择一个
    type_selected = set()
    for config in SNMP_OID_MAPPING:
        proc_type = config.get('data_processing', {}).get('type', 'direct')
        if proc_type not in type_selected:
            test_oids.append({
                'oid': config['oid'],
                'description': config['description'],
                'type': proc_type
            })
            type_selected.add(proc_type)
        
        if len(test_oids) >= 4:  # 最多测试4个
            break
    
    # 如果没有足够的类型，添加前几个
    if len(test_oids) < 3:
        for config in SNMP_OID_MAPPING[:3]:
            if not any(t['oid'] == config['oid'] for t in test_oids):
                test_oids.append({
                    'oid': config['oid'],
                    'description': config['description'],
                    'type': config.get('data_processing', {}).get('type', 'direct')
                })
    
    success_count = 0
    total_count = len(test_oids)
    
    # 首先尝试Python SNMP库
    print("尝试使用Python SNMP库...")
    python_success, python_result = test_with_python_snmp()
    if python_success:
        print(f"✓ Python SNMP测试成功: {python_result}")
        print()
    else:
        print(f"✗ Python SNMP测试失败: {python_result}")
        print()
    
    # 使用snmpget命令行工具测试
    print("使用snmpget命令行工具测试:")
    print("-" * 40)
    
    for i, test_oid in enumerate(test_oids, 1):
        print(f"测试 {i}/{total_count}: {test_oid['description']}")
        print(f"OID: {test_oid['oid']}")
        print(f"类型: {test_oid['type']}")
        
        success, result = run_snmpget(test_oid['oid'], host, port, community)
        
        if success:
            print(f"✓ 结果: {result}")
            success_count += 1
        else:
            print(f"✗ 错误: {result}")
        
        print("-" * 40)
        time.sleep(1)
    
    # 显示总结
    print(f"snmpget测试完成: {success_count}/{total_count} 成功")
    
    if success_count == total_count:
        print("🎉 所有测试通过！SNMP Agent工作正常")
    elif success_count > 0:
        print("⚠️  部分测试通过，请检查失败的OID")
    else:
        print("❌ 所有测试失败")
        print("可能的原因:")
        print("1. SNMP Agent未启动")
        print("2. 端口被占用")
        print("3. 防火墙阻止连接")
        print("4. snmpget工具未安装")
    
    return success_count, total_count

def test_single_oid(oid):
    """测试单个OID"""
    host = '127.0.0.1'
    port = SNMP_CONFIG.get('port', 161)
    community = SNMP_CONFIG.get('community', 'public')
    
    # 查找描述
    description = "未知"
    for config in SNMP_OID_MAPPING:
        if config['oid'] == oid:
            description = config['description']
            break
    
    print(f"测试单个OID")
    print("=" * 30)
    print(f"OID: {oid}")
    print(f"描述: {description}")
    print(f"目标: {host}:{port}")
    print()
    
    # 使用snmpget测试
    print("使用snmpget:")
    success, result = run_snmpget(oid, host, port, community)
    
    if success:
        print(f"✓ 成功: {result}")
    else:
        print(f"✗ 失败: {result}")
    
    # 尝试Python SNMP
    print("\n使用Python SNMP:")
    python_success, python_result = test_with_python_snmp()
    if python_success:
        print(f"✓ 成功: {python_result}")
    else:
        print(f"✗ 失败: {python_result}")

def show_test_commands():
    """显示测试命令"""
    print("SNMP测试命令")
    print("=" * 30)
    
    host = '127.0.0.1'
    port = SNMP_CONFIG.get('port', 161)
    community = SNMP_CONFIG.get('community', 'public')
    
    print("手动测试命令:")
    print(f"snmpget -v2c -c {community} {host}:{port} <OID>")
    print()
    
    print("示例:")
    for i, config in enumerate(SNMP_OID_MAPPING[:3], 1):
        oid = config['oid']
        description = config['description']
        print(f"{i}. {description}")
        print(f"   snmpget -v2c -c {community} {host}:{port} {oid}")
    
    print()
    print("获取所有OID:")
    print(f"snmpwalk -v2c -c {community} {host}:{port} *******.4.1.99999")

def main():
    """主函数"""
    if len(sys.argv) < 2:
        print("简单SNMP客户端测试工具")
        print("=" * 40)
        print("用法:")
        print("  python simple_snmp_client.py test           # 运行测试")
        print("  python simple_snmp_client.py oid <OID>      # 测试指定OID")
        print("  python simple_snmp_client.py commands       # 显示测试命令")
        print()
        print("示例:")
        print("  python simple_snmp_client.py oid *******.4.1.99999.1.0")
        return
    
    command = sys.argv[1].lower()
    
    try:
        if command == 'test':
            test_snmp_agent()
        
        elif command == 'oid':
            if len(sys.argv) < 3:
                print("请指定要测试的OID")
                return
            oid = sys.argv[2]
            test_single_oid(oid)
        
        elif command == 'commands':
            show_test_commands()
        
        else:
            print(f"未知命令: {command}")
            print("使用 'python simple_snmp_client.py' 查看帮助")
    
    except KeyboardInterrupt:
        print("\n测试被用户中断")
    except Exception as e:
        print(f"测试过程中发生错误: {e}")

if __name__ == "__main__":
    main()
