#!/usr/bin/env python3
"""
使用socket的SNMP测试
直接发送UDP包到SNMP Agent进行测试
"""

import socket
import time
import sys
from config import SNMP_OID_MAPPING, SNMP_CONFIG

def create_snmp_get_packet(oid, community='public'):
    """创建SNMP GET请求包 (简化版)"""
    # 这是一个简化的SNMP包构造，仅用于测试连通性
    # 实际的SNMP包格式更复杂
    
    # SNMP GET PDU的基本结构 (简化)
    # 这里只是发送一个基本的UDP包来测试端口是否开放
    message = f"SNMP_TEST:{community}:{oid}".encode('utf-8')
    return message

def test_udp_connection(host='127.0.0.1', port=161):
    """测试UDP连接"""
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        sock.settimeout(3)
        
        # 发送测试数据
        test_data = b"SNMP_CONNECTION_TEST"
        sock.sendto(test_data, (host, port))
        
        # 尝试接收响应 (可能没有响应，这是正常的)
        try:
            response, addr = sock.recvfrom(1024)
            sock.close()
            return True, f"收到响应: {len(response)} 字节"
        except socket.timeout:
            sock.close()
            return True, "端口开放 (无响应，正常)"
        
    except Exception as e:
        return False, f"连接失败: {e}"

def test_snmp_port():
    """测试SNMP端口"""
    print("SNMP端口连通性测试")
    print("=" * 40)
    
    host = '127.0.0.1'
    port = SNMP_CONFIG.get('port', 161)
    
    print(f"测试目标: {host}:{port}")
    
    success, result = test_udp_connection(host, port)
    
    if success:
        print(f"✓ {result}")
        return True
    else:
        print(f"✗ {result}")
        return False

def check_snmp_agent_log():
    """检查SNMP Agent是否有日志输出"""
    print("\nSNMP Agent状态检查:")
    print("-" * 30)
    
    # 检查是否有SNMP Agent进程在运行
    try:
        import psutil
        
        snmp_processes = []
        for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
            try:
                if proc.info['cmdline']:
                    cmdline = ' '.join(proc.info['cmdline'])
                    if 'modbus_snmp_agent.py' in cmdline or 'snmp' in proc.info['name'].lower():
                        snmp_processes.append(proc.info)
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                pass
        
        if snmp_processes:
            print("✓ 找到SNMP相关进程:")
            for proc in snmp_processes:
                print(f"  PID: {proc['pid']}, 名称: {proc['name']}")
        else:
            print("✗ 未找到SNMP相关进程")
            print("请确保已启动: python modbus_snmp_agent.py")
    
    except ImportError:
        print("⚠️  无法检查进程 (需要psutil库)")
        print("建议手动检查是否已启动: python modbus_snmp_agent.py")

def test_oid_accessibility():
    """测试OID可访问性"""
    print("\nOID配置检查:")
    print("-" * 30)
    
    print(f"配置的OID数量: {len(SNMP_OID_MAPPING)}")
    
    # 按类型统计
    type_counts = {}
    for config in SNMP_OID_MAPPING:
        proc_type = config.get('data_processing', {}).get('type', 'direct')
        type_counts[proc_type] = type_counts.get(proc_type, 0) + 1
    
    print("OID类型分布:")
    for proc_type, count in type_counts.items():
        print(f"  {proc_type}: {count} 个")
    
    print("\n前5个OID:")
    for i, config in enumerate(SNMP_OID_MAPPING[:5], 1):
        print(f"  {i}. {config['oid']} - {config['description']}")

def simulate_snmp_requests():
    """模拟SNMP请求"""
    print("\n模拟SNMP请求:")
    print("-" * 30)
    
    host = '127.0.0.1'
    port = SNMP_CONFIG.get('port', 161)
    community = SNMP_CONFIG.get('community', 'public')
    
    # 选择几个OID进行测试
    test_oids = SNMP_OID_MAPPING[:3]
    
    for i, config in enumerate(test_oids, 1):
        oid = config['oid']
        description = config['description']
        
        print(f"测试 {i}: {description}")
        print(f"OID: {oid}")
        
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            sock.settimeout(3)
            
            # 创建测试包
            packet = create_snmp_get_packet(oid, community)
            
            # 发送请求
            sock.sendto(packet, (host, port))
            
            # 尝试接收响应
            try:
                response, addr = sock.recvfrom(1024)
                print(f"✓ 收到响应: {len(response)} 字节")
                
                # 尝试解析响应 (简单检查)
                if len(response) > 0:
                    print(f"  响应内容: {response[:50]}...")
                
            except socket.timeout:
                print("⚠️  请求超时 (可能正常，取决于Agent实现)")
            
            sock.close()
            
        except Exception as e:
            print(f"✗ 请求失败: {e}")
        
        print("-" * 25)
        time.sleep(1)

def show_manual_test_commands():
    """显示手动测试命令"""
    print("\n手动测试建议:")
    print("-" * 30)
    
    host = '127.0.0.1'
    port = SNMP_CONFIG.get('port', 161)
    community = SNMP_CONFIG.get('community', 'public')
    
    print("如果有net-snmp工具，可以使用:")
    print(f"snmpget -v2c -c {community} {host}:{port} *******.4.1.99999.1.0")
    print(f"snmpwalk -v2c -c {community} {host}:{port} *******.4.1.99999")
    
    print("\n如果有Python pysnmp库，可以使用:")
    print("from pysnmp.hlapi import *")
    print("# 然后使用getCmd函数")
    
    print("\n检查Agent状态:")
    print("1. 确认modbus_snmp_agent.py正在运行")
    print("2. 检查日志输出是否有错误")
    print("3. 确认端口161未被其他程序占用")

def main():
    """主函数"""
    if len(sys.argv) > 1:
        command = sys.argv[1].lower()
        
        if command == 'port':
            test_snmp_port()
        elif command == 'process':
            check_snmp_agent_log()
        elif command == 'oids':
            test_oid_accessibility()
        elif command == 'simulate':
            simulate_snmp_requests()
        elif command == 'commands':
            show_manual_test_commands()
        else:
            print(f"未知命令: {command}")
            print("可用命令: port, process, oids, simulate, commands")
    else:
        # 运行完整测试
        print("Socket SNMP测试工具")
        print("=" * 50)
        
        # 1. 测试端口连通性
        port_ok = test_snmp_port()
        
        # 2. 检查进程状态
        check_snmp_agent_log()
        
        # 3. 显示OID信息
        test_oid_accessibility()
        
        # 4. 如果端口可达，尝试模拟请求
        if port_ok:
            simulate_snmp_requests()
        
        # 5. 显示手动测试建议
        show_manual_test_commands()
        
        print("\n" + "=" * 50)
        print("测试完成")
        
        if port_ok:
            print("✓ SNMP端口可达，Agent可能正在运行")
        else:
            print("✗ SNMP端口不可达，请检查Agent是否启动")

if __name__ == "__main__":
    main()
