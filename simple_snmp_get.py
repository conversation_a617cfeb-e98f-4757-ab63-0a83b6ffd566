#!/usr/bin/env python3
"""
简单的SNMP GET测试脚本
使用同步方式进行SNMP查询
"""

import sys
import time
from pysnmp.hlapi import getCmd, SnmpEngine, CommunityData, UdpTransportTarget, ContextData, ObjectType, ObjectIdentity
from config import SNMP_OID_MAPPING, SNMP_CONFIG

class SimpleSNMPTester:
    """简单的SNMP测试器"""
    
    def __init__(self, host='127.0.0.1', port=161, community='public'):
        self.host = host
        self.port = port
        self.community = community
        self.results = []
    
    def get_oid_value(self, oid):
        """获取单个OID的值"""
        try:
            for (errorIndication, errorStatus, errorIndex, varBinds) in getCmd(
                SnmpEngine(),
                CommunityData(self.community),
                UdpTransportTarget((self.host, self.port)),
                ContextData(),
                ObjectType(ObjectIdentity(oid))
            ):
                if errorIndication:
                    return False, f"错误指示: {errorIndication}"
                elif errorStatus:
                    return False, f"状态错误: {errorStatus.prettyPrint()}"
                else:
                    for varBind in varBinds:
                        return True, varBind[1].prettyPrint()
        except Exception as e:
            return False, f"异常: {e}"
        
        return False, "未知错误"
    
    def test_oid(self, oid, description=""):
        """测试单个OID并显示结果"""
        print(f"测试OID: {oid}")
        if description:
            print(f"描述: {description}")
        
        success, result = self.get_oid_value(oid)
        
        if success:
            print(f"✓ 值: {result}")
            status = "成功"
        else:
            print(f"✗ 错误: {result}")
            status = "失败"
        
        print("-" * 50)
        
        # 记录结果
        self.results.append({
            'oid': oid,
            'description': description,
            'success': success,
            'value': result if success else None,
            'error': result if not success else None
        })
        
        return success, result
    
    def test_all_oids(self):
        """测试所有配置的OID"""
        print("测试所有配置的OID")
        print("=" * 60)
        
        for config in SNMP_OID_MAPPING:
            oid = config['oid']
            description = config['description']
            self.test_oid(oid, description)
            time.sleep(0.5)  # 避免请求过快
    
    def test_by_type(self, oid_type):
        """按类型测试OID"""
        type_names = {
            'multiply': '数值处理',
            'equal_return': '状态判断', 
            'direct': '直接映射',
            'communication_status': '通讯状态'
        }
        
        print(f"测试 {type_names.get(oid_type, oid_type)} 类型的OID")
        print("=" * 60)
        
        found = False
        for config in SNMP_OID_MAPPING:
            processing_type = config.get('data_processing', {}).get('type', 'direct')
            if processing_type == oid_type:
                found = True
                self.test_oid(config['oid'], config['description'])
                time.sleep(0.5)
        
        if not found:
            print(f"未找到 {oid_type} 类型的OID")
    
    def monitor_oids(self, oid_list, interval=5, count=10):
        """监控指定OID的值变化"""
        print(f"监控OID值变化 (间隔: {interval}秒, 次数: {count})")
        print("=" * 60)
        
        # 获取OID描述
        oid_descriptions = {}
        for oid in oid_list:
            for config in SNMP_OID_MAPPING:
                if config['oid'] == oid:
                    oid_descriptions[oid] = config['description']
                    break
            if oid not in oid_descriptions:
                oid_descriptions[oid] = "未知"
        
        for i in range(count):
            print(f"\n第 {i+1} 次监控 ({time.strftime('%H:%M:%S')}):")
            print("-" * 40)
            
            for oid in oid_list:
                success, value = self.get_oid_value(oid)
                description = oid_descriptions[oid]
                
                if success:
                    print(f"{description}: {value}")
                else:
                    print(f"{description}: 错误 - {value}")
            
            if i < count - 1:  # 最后一次不需要等待
                time.sleep(interval)
        
        print(f"\n监控完成")
    
    def show_summary(self):
        """显示测试总结"""
        if not self.results:
            print("没有测试结果")
            return
        
        print("\n测试总结")
        print("=" * 60)
        
        total = len(self.results)
        success_count = sum(1 for r in self.results if r['success'])
        fail_count = total - success_count
        
        print(f"总测试数: {total}")
        print(f"成功: {success_count}")
        print(f"失败: {fail_count}")
        print(f"成功率: {success_count/total*100:.1f}%")
        
        if fail_count > 0:
            print("\n失败的测试:")
            for result in self.results:
                if not result['success']:
                    print(f"  {result['oid']} ({result['description']}): {result['error']}")
    
    def show_oid_list(self):
        """显示所有可用的OID"""
        print("可用的OID列表")
        print("=" * 60)
        
        # 按类型分组
        types = {}
        for config in SNMP_OID_MAPPING:
            proc_type = config.get('data_processing', {}).get('type', 'direct')
            if proc_type not in types:
                types[proc_type] = []
            types[proc_type].append(config)
        
        type_names = {
            'multiply': '数值处理 (乘以系数)',
            'equal_return': '状态判断 (等值返回)',
            'direct': '直接映射',
            'communication_status': '通讯状态'
        }
        
        for proc_type, configs in types.items():
            print(f"\n{type_names.get(proc_type, proc_type)}:")
            print("-" * 40)
            for config in configs:
                print(f"  {config['oid']} - {config['description']}")

def main():
    """主函数"""
    # 从配置读取SNMP参数
    host = '127.0.0.1'
    port = SNMP_CONFIG.get('port', 161)
    community = SNMP_CONFIG.get('community', 'public')
    
    tester = SimpleSNMPTester(host, port, community)
    
    if len(sys.argv) < 2:
        print("简单SNMP GET测试工具")
        print("=" * 60)
        print("用法:")
        print("  python simple_snmp_get.py all                    # 测试所有OID")
        print("  python simple_snmp_get.py list                   # 显示所有可用OID")
        print("  python simple_snmp_get.py oid <OID>              # 测试指定OID")
        print("  python simple_snmp_get.py type <类型>            # 测试指定类型")
        print("  python simple_snmp_get.py monitor <OID1,OID2>    # 监控指定OID")
        print()
        print("类型选项: multiply, equal_return, direct, communication_status")
        print()
        print("示例:")
        print("  python simple_snmp_get.py oid *******.4.1.99999.1.0")
        print("  python simple_snmp_get.py type multiply")
        print("  python simple_snmp_get.py monitor *******.4.1.99999.1.0,*******.4.1.99999.999.1")
        return
    
    command = sys.argv[1].lower()
    
    try:
        if command == 'all':
            tester.test_all_oids()
            tester.show_summary()
        
        elif command == 'list':
            tester.show_oid_list()
        
        elif command == 'oid':
            if len(sys.argv) < 3:
                print("请指定要测试的OID")
                return
            oid = sys.argv[2]
            
            # 查找描述
            description = ""
            for config in SNMP_OID_MAPPING:
                if config['oid'] == oid:
                    description = config['description']
                    break
            
            tester.test_oid(oid, description)
        
        elif command == 'type':
            if len(sys.argv) < 3:
                print("请指定要测试的类型")
                print("可用类型: multiply, equal_return, direct, communication_status")
                return
            oid_type = sys.argv[2]
            tester.test_by_type(oid_type)
            tester.show_summary()
        
        elif command == 'monitor':
            if len(sys.argv) < 3:
                print("请指定要监控的OID列表 (用逗号分隔)")
                return
            oid_list = [oid.strip() for oid in sys.argv[2].split(',')]
            
            # 可选参数
            interval = 5
            count = 10
            if len(sys.argv) >= 4:
                interval = int(sys.argv[3])
            if len(sys.argv) >= 5:
                count = int(sys.argv[4])
            
            tester.monitor_oids(oid_list, interval, count)
        
        else:
            print(f"未知命令: {command}")
            print("使用 'python simple_snmp_get.py' 查看帮助")
    
    except KeyboardInterrupt:
        print("\n测试被用户中断")
    except Exception as e:
        print(f"测试过程中发生错误: {e}")

if __name__ == "__main__":
    main()
