#!/usr/bin/env python3
"""
Modbus to SNMPv2 Agent
实现从Modbus设备读取0x100寄存器数据并通过SNMPv2协议提供访问
"""

from pysnmp.entity import engine, config
from pysnmp.entity.rfc3413 import cmdrsp, context
from pysnmp.proto import rfc1902
from pysnmp.carrier.asyncio.dgram import udp
from pymodbus.client import ModbusTcpClient, ModbusSerialClient
import threading
import time
import logging
import struct
from config import (
    MODBUS_TYPE, MODBUS_TCP_CONFIG, MODBUS_RTU_CONFIG,
    SNMP_OID_MAPPING, SNMP_CONFIG, LOG_CONFIG, MISC_CONFIG
)

# 配置日志
try:
    log_level = getattr(logging, LOG_CONFIG['level'])
    logging.basicConfig(
        level=log_level,
        format=LOG_CONFIG['format'],
        filename=LOG_CONFIG.get('file')
    )
except:
    # 回退到默认配置
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
logger = logging.getLogger(__name__)

# 全局变量存储最新的数据
oid_data_cache = {}  # {oid: {'value': value, 'timestamp': timestamp, 'error': error_msg, 'raw_value': raw_value}}
communication_status = {}  # {unit_id: {'connected': bool, 'last_success': timestamp, 'error_count': int}}
data_lock = threading.Lock()

class ModbusReader:
    """Modbus数据读取器 - 支持TCP和RTU"""

    def __init__(self):
        self.client = None
        self.connected = False
        self.modbus_type = MODBUS_TYPE

    def connect(self):
        """连接到Modbus设备"""
        try:
            if self.modbus_type == 'TCP':
                self.client = ModbusTcpClient(
                    MODBUS_TCP_CONFIG['server_ip'],
                    port=MODBUS_TCP_CONFIG['port'],
                    timeout=MODBUS_TCP_CONFIG['timeout']
                )
                logger.info(f"尝试连接Modbus TCP: {MODBUS_TCP_CONFIG['server_ip']}:{MODBUS_TCP_CONFIG['port']}")
            elif self.modbus_type == 'RTU':
                self.client = ModbusSerialClient(
                    port=MODBUS_RTU_CONFIG['port'],
                    baudrate=MODBUS_RTU_CONFIG['baudrate'],
                    bytesize=MODBUS_RTU_CONFIG['bytesize'],
                    parity=MODBUS_RTU_CONFIG['parity'],
                    stopbits=MODBUS_RTU_CONFIG['stopbits'],
                    timeout=MODBUS_RTU_CONFIG['timeout']
                )
                logger.info(f"尝试连接Modbus RTU: {MODBUS_RTU_CONFIG['port']} @ {MODBUS_RTU_CONFIG['baudrate']}")
            else:
                raise Exception(f"不支持的Modbus类型: {self.modbus_type}")

            self.connected = self.client.connect()
            if self.connected:
                logger.info(f"Modbus {self.modbus_type} 连接成功")
            else:
                logger.error(f"Modbus {self.modbus_type} 连接失败")
            return self.connected
        except Exception as e:
            logger.error(f"Modbus连接异常: {e}")
            self.connected = False
            return False

    def disconnect(self):
        """断开Modbus连接"""
        if self.client:
            self.client.close()
            self.connected = False
            logger.info(f"Modbus {self.modbus_type} 连接已断开")

    def convert_data_type(self, raw_value, data_type):
        """转换数据类型"""
        try:
            if data_type == 'uint16':
                return int(raw_value) & 0xFFFF
            elif data_type == 'int16':
                # 处理有符号16位整数
                if raw_value > 32767:
                    return raw_value - 65536
                return raw_value
            elif data_type == 'uint32':
                # 需要读取两个寄存器
                return raw_value  # 简化处理，实际应该读取两个寄存器
            elif data_type == 'int32':
                return raw_value  # 简化处理
            elif data_type == 'float32':
                # 需要读取两个寄存器并转换为浮点数
                return float(raw_value)  # 简化处理
            else:
                return raw_value
        except Exception as e:
            logger.warning(f"数据类型转换失败 {data_type}: {e}")
            return raw_value

    def read_register(self, register_config):
        """读取指定配置的Modbus寄存器数据"""
        if not self.connected:
            if not self.connect():
                raise Exception("无法连接到Modbus设备")

        try:
            address = register_config['register_address']
            unit_id = register_config['unit_id']
            function_code = register_config['function_code']
            data_type = register_config['data_type']

            response = None

            # 根据功能码选择读取方法
            if function_code == 3:  # 读保持寄存器
                response = self._read_holding_registers(address, unit_id)
            elif function_code == 4:  # 读输入寄存器
                response = self._read_input_registers(address, unit_id)
            elif function_code == 1:  # 读线圈
                response = self._read_coils(address, unit_id)
            elif function_code == 2:  # 读离散输入
                response = self._read_discrete_inputs(address, unit_id)
            else:
                raise Exception(f"不支持的功能码: {function_code}")

            if response and not response.isError():
                if function_code in [1, 2]:  # 线圈和离散输入返回位值
                    raw_value = response.bits[0]
                else:  # 寄存器返回数值
                    raw_value = response.registers[0]

                # 转换数据类型
                converted_value = self.convert_data_type(raw_value, data_type)

                logger.debug(f"读取寄存器 0x{address:X} (单元{unit_id}, 功能码{function_code}): {converted_value}")
                return converted_value
            else:
                raise Exception(f"Modbus读取错误: {response}")

        except Exception as e:
            logger.error(f"读取Modbus寄存器失败: {e}")
            self.connected = False
            raise

    def _read_holding_registers(self, address, unit_id):
        """读保持寄存器 - 兼容不同API版本"""
        # 尝试不同的API调用方式
        methods = [
            lambda: self.client.read_holding_registers(address, count=1, slave=unit_id),
            lambda: self.client.read_holding_registers(address, count=1, unit=unit_id),
            lambda: self.client.read_holding_registers(address, 1, slave=unit_id),
            lambda: self.client.read_holding_registers(address, 1, unit=unit_id),
            lambda: self.client.read_holding_registers(address, 1),
            lambda: self.client.read_holding_registers(address)
        ]

        for method in methods:
            try:
                return method()
            except (TypeError, AttributeError):
                continue

        raise Exception("无法找到兼容的API调用方式")

    def _read_input_registers(self, address, unit_id):
        """读输入寄存器 - 兼容不同API版本"""
        methods = [
            lambda: self.client.read_input_registers(address, count=1, slave=unit_id),
            lambda: self.client.read_input_registers(address, count=1, unit=unit_id),
            lambda: self.client.read_input_registers(address, 1, slave=unit_id),
            lambda: self.client.read_input_registers(address, 1, unit=unit_id),
            lambda: self.client.read_input_registers(address, 1),
            lambda: self.client.read_input_registers(address)
        ]

        for method in methods:
            try:
                return method()
            except (TypeError, AttributeError):
                continue

        raise Exception("无法找到兼容的API调用方式")

    def _read_coils(self, address, unit_id):
        """读线圈 - 兼容不同API版本"""
        methods = [
            lambda: self.client.read_coils(address, count=1, slave=unit_id),
            lambda: self.client.read_coils(address, count=1, unit=unit_id),
            lambda: self.client.read_coils(address, 1, slave=unit_id),
            lambda: self.client.read_coils(address, 1, unit=unit_id),
            lambda: self.client.read_coils(address, 1),
            lambda: self.client.read_coils(address)
        ]

        for method in methods:
            try:
                return method()
            except (TypeError, AttributeError):
                continue

        raise Exception("无法找到兼容的API调用方式")

    def _read_discrete_inputs(self, address, unit_id):
        """读离散输入 - 兼容不同API版本"""
        methods = [
            lambda: self.client.read_discrete_inputs(address, count=1, slave=unit_id),
            lambda: self.client.read_discrete_inputs(address, count=1, unit=unit_id),
            lambda: self.client.read_discrete_inputs(address, 1, slave=unit_id),
            lambda: self.client.read_discrete_inputs(address, 1, unit=unit_id),
            lambda: self.client.read_discrete_inputs(address, 1),
            lambda: self.client.read_discrete_inputs(address)
        ]

        for method in methods:
            try:
                return method()
            except (TypeError, AttributeError):
                continue

        raise Exception("无法找到兼容的API调用方式")

    def read_register_by_config(self, modbus_config):
        """根据配置读取Modbus寄存器"""
        if not self.connected:
            if not self.connect():
                raise Exception("无法连接到Modbus设备")

        try:
            address = modbus_config['register_address']
            unit_id = modbus_config['unit_id']
            function_code = modbus_config['function_code']
            data_type = modbus_config['data_type']
            register_count = modbus_config.get('register_count', 1)

            response = None

            # 根据功能码选择读取方法
            if function_code == 3:  # 读保持寄存器
                response = self._read_holding_registers_count(address, unit_id, register_count)
            elif function_code == 4:  # 读输入寄存器
                response = self._read_input_registers_count(address, unit_id, register_count)
            elif function_code == 1:  # 读线圈
                response = self._read_coils(address, unit_id)
            elif function_code == 2:  # 读离散输入
                response = self._read_discrete_inputs(address, unit_id)
            else:
                raise Exception(f"不支持的功能码: {function_code}")

            if response and not response.isError():
                if function_code in [1, 2]:  # 线圈和离散输入返回位值
                    raw_value = 1 if response.bits[0] else 0
                else:  # 寄存器返回数值
                    if data_type == 'uint32' and register_count >= 2:
                        # 32位数据，组合两个16位寄存器
                        raw_value = (response.registers[0] << 16) + response.registers[1]
                    elif data_type == 'int32' and register_count >= 2:
                        # 有符号32位数据
                        raw_value = (response.registers[0] << 16) + response.registers[1]
                        if raw_value > 2147483647:  # 处理负数
                            raw_value -= 4294967296
                    else:
                        raw_value = response.registers[0]
                        # 处理有符号16位数据
                        if data_type == 'int16' and raw_value > 32767:
                            raw_value -= 65536

                logger.debug(f"读取寄存器 0x{address:X} (单元{unit_id}, 功能码{function_code}): {raw_value}")
                return raw_value
            else:
                raise Exception(f"Modbus读取错误: {response}")

        except Exception as e:
            logger.error(f"读取Modbus寄存器失败: {e}")
            self.connected = False
            raise

    def _read_holding_registers_count(self, address, unit_id, count):
        """读保持寄存器 - 支持指定数量"""
        methods = [
            lambda: self.client.read_holding_registers(address, count=count, slave=unit_id),
            lambda: self.client.read_holding_registers(address, count=count, unit=unit_id),
            lambda: self.client.read_holding_registers(address, count, slave=unit_id),
            lambda: self.client.read_holding_registers(address, count, unit=unit_id),
            lambda: self.client.read_holding_registers(address, count),
        ]

        for method in methods:
            try:
                return method()
            except (TypeError, AttributeError):
                continue

        raise Exception("无法找到兼容的API调用方式")

    def _read_input_registers_count(self, address, unit_id, count):
        """读输入寄存器 - 支持指定数量"""
        methods = [
            lambda: self.client.read_input_registers(address, count=count, slave=unit_id),
            lambda: self.client.read_input_registers(address, count=count, unit=unit_id),
            lambda: self.client.read_input_registers(address, count, slave=unit_id),
            lambda: self.client.read_input_registers(address, count, unit=unit_id),
            lambda: self.client.read_input_registers(address, count),
        ]

        for method in methods:
            try:
                return method()
            except (TypeError, AttributeError):
                continue

        raise Exception("无法找到兼容的API调用方式")

# 创建Modbus读取器实例
modbus_reader = ModbusReader()

def process_data_by_type(raw_value, processing_config):
    """根据配置处理原始数据"""
    try:
        process_type = processing_config.get('type', 'direct')

        if process_type == 'multiply':
            # 原数据乘以系数加偏移
            coefficient = processing_config.get('coefficient', 1)
            offset = processing_config.get('offset', 0)
            result = (raw_value * coefficient) + offset

            # 处理小数位数
            decimal_places = processing_config.get('decimal_places', 0)
            if decimal_places > 0:
                result = round(result, decimal_places)
            else:
                result = int(result)

            return result

        elif process_type == 'equal_return':
            # 等于某值时返回指定值，否则返回默认值
            trigger_value = processing_config.get('trigger_value')
            return_value = processing_config.get('return_value', 1)
            default_value = processing_config.get('default_value', 0)

            return return_value if raw_value == trigger_value else default_value

        elif process_type == 'direct':
            # 直接返回原数据
            return raw_value

        elif process_type == 'communication_status':
            # 通讯状态处理在其他地方
            return raw_value

        else:
            logger.warning(f"未知的数据处理类型: {process_type}")
            return raw_value

    except Exception as e:
        logger.error(f"数据处理失败: {e}")
        return raw_value

def initialize_oid_cache():
    """初始化OID缓存"""
    global oid_data_cache

    current_time = time.time()

    # 初始化所有OID的缓存
    for oid_config in SNMP_OID_MAPPING:
        oid = oid_config['oid']
        fault_value = oid_config.get('fault_value', -99998)

        # 通讯状态OID初始为故障状态
        if oid_config.get('data_processing', {}).get('type') == 'communication_status':
            initial_value = 0  # 通讯状态初始为故障
        else:
            initial_value = fault_value  # 其他OID初始为故障值

        oid_data_cache[oid] = {
            'value': initial_value,
            'raw_value': None,
            'timestamp': current_time,
            'error': '初始化状态',
            'description': oid_config['description']
        }

    logger.info(f"初始化了 {len(oid_data_cache)} 个OID缓存")

def update_communication_status(unit_id, success, error_msg=None):
    """更新通讯状态"""
    global communication_status, oid_data_cache

    current_time = time.time()

    if unit_id not in communication_status:
        communication_status[unit_id] = {
            'connected': False,
            'last_success': 0,
            'error_count': 0,
            'last_error': None
        }

    if success:
        communication_status[unit_id]['connected'] = True
        communication_status[unit_id]['last_success'] = current_time
        communication_status[unit_id]['error_count'] = 0
        communication_status[unit_id]['last_error'] = None
    else:
        communication_status[unit_id]['connected'] = False
        communication_status[unit_id]['error_count'] += 1
        communication_status[unit_id]['last_error'] = error_msg

    # 更新通讯状态OID
    for oid_config in SNMP_OID_MAPPING:
        processing = oid_config.get('data_processing', {})
        if processing.get('type') == 'communication_status':
            oid = oid_config['oid']
            scope = processing.get('scope')

            if scope == 'global':
                # 全局通讯状态 - 任何一个从站连接成功就是正常
                any_connected = any(status['connected'] for status in communication_status.values())
                value = 1 if any_connected else 0
            elif scope == 'unit':
                # 特定从站的通讯状态
                target_unit_id = processing.get('unit_id')
                if target_unit_id == unit_id:
                    status = communication_status.get(unit_id, {'connected': False})
                    value = 1 if status['connected'] else 0
                else:
                    continue  # 不是当前从站，跳过
            else:
                continue

            with data_lock:
                oid_data_cache[oid] = {
                    'value': value,
                    'raw_value': value,
                    'timestamp': current_time,
                    'error': None if success else error_msg,
                    'description': oid_config['description']
                }

def update_oid_data():
    """定期更新OID数据的后台线程"""
    global oid_data_cache

    # 获取更新间隔
    if MODBUS_TYPE == 'TCP':
        update_interval = MODBUS_TCP_CONFIG['update_interval']
        retry_interval = MODBUS_TCP_CONFIG['retry_interval']
    else:
        update_interval = MODBUS_RTU_CONFIG['update_interval']
        retry_interval = MODBUS_RTU_CONFIG['retry_interval']

    # 初始化OID缓存
    initialize_oid_cache()

    while True:
        overall_success = False
        unit_success = {}  # 记录每个从站的成功状态

        try:
            # 遍历所有OID配置
            for oid_config in SNMP_OID_MAPPING:
                oid = oid_config['oid']
                modbus_config = oid_config.get('modbus_config')
                processing_config = oid_config.get('data_processing', {})
                fault_value = oid_config.get('fault_value', -99998)

                # 跳过通讯状态OID，它们在其他地方处理
                if processing_config.get('type') == 'communication_status':
                    continue

                if not modbus_config:
                    continue

                unit_id = modbus_config['unit_id']

                try:
                    # 读取Modbus数据
                    raw_value = modbus_reader.read_register_by_config(modbus_config)

                    # 处理数据
                    processed_value = process_data_by_type(raw_value, processing_config)

                    # 更新缓存
                    with data_lock:
                        oid_data_cache[oid] = {
                            'value': processed_value,
                            'raw_value': raw_value,
                            'timestamp': time.time(),
                            'error': None,
                            'description': oid_config['description']
                        }

                    logger.debug(f"更新 {oid_config['description']} (OID: {oid}): 原值={raw_value}, 处理后={processed_value}")

                    # 标记此从站通讯成功
                    unit_success[unit_id] = True
                    overall_success = True

                except Exception as e:
                    error_msg = f"读取失败: {e}"

                    # 更新错误状态
                    with data_lock:
                        oid_data_cache[oid] = {
                            'value': fault_value,
                            'raw_value': None,
                            'timestamp': time.time(),
                            'error': error_msg,
                            'description': oid_config['description']
                        }

                    logger.error(f"读取 {oid_config['description']} 失败: {e}")

                    # 标记此从站通讯失败
                    if unit_id not in unit_success:
                        unit_success[unit_id] = False

            # 更新通讯状态
            for unit_id, success in unit_success.items():
                update_communication_status(unit_id, success)

            # 如果没有任何从站成功，更新全局通讯状态
            if not overall_success:
                update_communication_status(0, False, "所有从站通讯失败")

        except Exception as e:
            logger.error(f"更新OID数据异常: {e}")
            # 更新全局通讯状态为失败
            update_communication_status(0, False, str(e))
            # 连接失败时等待更长时间再重试
            time.sleep(retry_interval)
            continue

        # 按配置的间隔更新数据
        time.sleep(update_interval)

class ModbusSnmpResponder(cmdrsp.GetCommandResponder):
    """SNMP GET请求响应器 - 基于OID配置的新架构"""

    def handleMgmtOperation(self, snmp_engine, state_reference, context_name, var_binds, cb_ctx):
        """处理SNMP GET请求"""
        response_var_binds = []

        # 创建所有支持的OID集合
        supported_oids = {config['oid'] for config in SNMP_OID_MAPPING}

        for oid, _ in var_binds:
            oid_str = str(oid)
            logger.info(f"收到SNMP请求，OID: {oid_str}")

            if oid_str in supported_oids:
                try:
                    # 从缓存获取数据
                    with data_lock:
                        cached_data = oid_data_cache.get(oid_str)

                    if cached_data:
                        value = cached_data['value']
                        error = cached_data.get('error')
                        description = cached_data.get('description', 'Unknown')
                        raw_value = cached_data.get('raw_value')

                        if error:
                            logger.warning(f"返回错误数据 {description}: {value} (错误: {error})")
                        else:
                            logger.info(f"返回 {description}: {value} (原值: {raw_value})")

                        # 根据值的类型选择SNMP数据类型
                        if isinstance(value, float):
                            # 浮点数转换为字符串
                            response_var = rfc1902.OctetString(str(value))
                        else:
                            # 整数类型
                            response_var = rfc1902.Integer(int(value))

                        response_var_binds.append((oid, response_var))
                    else:
                        # 没有缓存数据
                        logger.warning(f"OID {oid_str} 没有缓存数据")
                        response_var_binds.append((oid, rfc1902.Integer(-99998)))

                except Exception as e:
                    logger.error(f"处理SNMP请求失败: {e}")
                    # 返回故障值
                    response_var_binds.append((oid, rfc1902.Integer(-99998)))
            else:
                # OID不匹配
                logger.warning(f"请求的OID不支持: {oid_str}")
                response_var_binds.append((oid, rfc1902.noSuchObject))

        # 发送响应
        self.sendRsp(snmp_engine, state_reference, 0, 0, response_var_binds)

def create_snmp_engine():
    """创建和配置SNMP引擎"""
    # 创建SNMP引擎
    snmp_engine = engine.SnmpEngine()

    # 获取SNMP配置
    community = SNMP_CONFIG.get('community', 'public')
    port = SNMP_CONFIG.get('port', 161)
    bind_address = SNMP_CONFIG.get('bind_address', '0.0.0.0')

    # 配置SNMPv2c社区 (使用新API)
    try:
        config.add_v1_system(snmp_engine, "my-area", community)
    except AttributeError:
        # 回退到旧API
        config.addV1System(snmp_engine, "my-area", community)

    # 配置UDP传输 (使用新API)
    try:
        config.add_transport(
            snmp_engine,
            udp.DOMAIN_NAME,
            udp.UdpTransport().open_server_mode((bind_address, port))
        )
    except AttributeError:
        # 回退到旧API
        config.addTransport(
            snmp_engine,
            udp.domainName,
            udp.UdpTransport().openServerMode((bind_address, port))
        )

    # 添加SNMP上下文
    snmp_context = context.SnmpContext(snmp_engine)

    # 注册响应器
    ModbusSnmpResponder(snmp_engine, snmp_context)

    return snmp_engine

def start_snmp_agent():
    """启动SNMP Agent"""
    snmp_engine = create_snmp_engine()

    # 获取配置信息
    port = SNMP_CONFIG.get('port', 161)
    community = SNMP_CONFIG.get('community', 'public')

    logger.info(f"SNMP Agent已启动，监听端口 {port}")
    logger.info(f"社区字符串: {community}")
    logger.info(f"Modbus类型: {MODBUS_TYPE}")

    # 显示支持的OID
    logger.info("支持的OID映射:")

    # 按处理类型分组显示
    multiply_oids = []
    equal_return_oids = []
    direct_oids = []
    comm_status_oids = []

    for oid_config in SNMP_OID_MAPPING:
        processing_type = oid_config.get('data_processing', {}).get('type', 'direct')
        if processing_type == 'multiply':
            multiply_oids.append(oid_config)
        elif processing_type == 'equal_return':
            equal_return_oids.append(oid_config)
        elif processing_type == 'communication_status':
            comm_status_oids.append(oid_config)
        else:
            direct_oids.append(oid_config)

    if multiply_oids:
        logger.info("  数值处理OID (乘以系数):")
        for config in multiply_oids:
            modbus_cfg = config['modbus_config']
            processing = config['data_processing']
            logger.info(f"    OID: {config['oid']} -> {config['description']}")
            logger.info(f"      寄存器: 0x{modbus_cfg['register_address']:X} (单元{modbus_cfg['unit_id']}, 功能码{modbus_cfg['function_code']})")
            logger.info(f"      处理: 原值 × {processing['coefficient']} + {processing.get('offset', 0)}")

    if equal_return_oids:
        logger.info("  状态判断OID (等值返回):")
        for config in equal_return_oids:
            modbus_cfg = config['modbus_config']
            processing = config['data_processing']
            logger.info(f"    OID: {config['oid']} -> {config['description']}")
            logger.info(f"      寄存器: 0x{modbus_cfg['register_address']:X} (单元{modbus_cfg['unit_id']}, 功能码{modbus_cfg['function_code']})")
            logger.info(f"      处理: 当原值={processing['trigger_value']}时返回{processing['return_value']}, 否则返回{processing['default_value']}")

    if direct_oids:
        logger.info("  直接映射OID:")
        for config in direct_oids:
            modbus_cfg = config['modbus_config']
            logger.info(f"    OID: {config['oid']} -> {config['description']}")
            logger.info(f"      寄存器: 0x{modbus_cfg['register_address']:X} (单元{modbus_cfg['unit_id']}, 功能码{modbus_cfg['function_code']})")

    if comm_status_oids:
        logger.info("  通讯状态OID:")
        for config in comm_status_oids:
            processing = config['data_processing']
            scope_info = f" ({processing['scope']})" if processing.get('scope') == 'global' else f" (单元{processing.get('unit_id')})"
            logger.info(f"    OID: {config['oid']} -> {config['description']}{scope_info}")

    try:
        # 使用新API或回退到旧API
        try:
            snmp_engine.transport_dispatcher.job_started(1)
            snmp_engine.transport_dispatcher.run_dispatcher()
        except AttributeError:
            # 回退到旧API
            snmp_engine.transportDispatcher.jobStarted(1)
            snmp_engine.transportDispatcher.runDispatcher()
    except Exception as e:
        logger.error(f"SNMP Agent运行异常: {e}")
        try:
            snmp_engine.transport_dispatcher.close_dispatcher()
        except AttributeError:
            snmp_engine.transportDispatcher.closeDispatcher()
        raise

def main():
    """主函数"""
    logger.info("启动Modbus到SNMPv2 Agent (增强版)")
    logger.info(f"Modbus类型: {MODBUS_TYPE}")

    if MODBUS_TYPE == 'TCP':
        logger.info(f"Modbus TCP设备: {MODBUS_TCP_CONFIG['server_ip']}:{MODBUS_TCP_CONFIG['port']}")
    else:
        logger.info(f"Modbus RTU设备: {MODBUS_RTU_CONFIG['port']} @ {MODBUS_RTU_CONFIG['baudrate']}")

    logger.info(f"配置了 {len(SNMP_OID_MAPPING)} 个OID映射")

    # 统计不同类型的OID数量
    type_counts = {}
    for oid_config in SNMP_OID_MAPPING:
        processing_type = oid_config.get('data_processing', {}).get('type', 'direct')
        type_counts[processing_type] = type_counts.get(processing_type, 0) + 1

    for proc_type, count in type_counts.items():
        logger.info(f"  {proc_type} 类型: {count} 个")

    try:
        # 启动OID数据更新线程
        oid_thread = threading.Thread(target=update_oid_data, daemon=True)
        oid_thread.start()
        logger.info("OID数据更新线程已启动")

        # 等待一下让Modbus连接建立
        startup_delay = MISC_CONFIG.get('startup_delay', 2)
        time.sleep(startup_delay)

        # 启动SNMP Agent (阻塞运行)
        start_snmp_agent()

    except KeyboardInterrupt:
        logger.info("收到停止信号")
    except Exception as e:
        logger.error(f"程序运行异常: {e}")
    finally:
        modbus_reader.disconnect()
        logger.info("程序已停止")

if __name__ == "__main__":
    main()
